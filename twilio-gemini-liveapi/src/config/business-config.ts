// Business Logic Configuration System
// Replaces hardcoded timeouts, thresholds, validation rules, and business constants

import { config, getConfigValue } from './config';

interface CallTimeouts {
    default: number;
    intro: number;
    response: number;
    transfer: number;
    recording: number;
    silence: number;
}

interface ValidationRule<T = any> {
    min: number;
    max: number;
    defaultErrorMessage?: string;
    defaultDisqualificationMessage?: string;
    minLength?: number;
    maxLength?: number;
    allowInternational?: boolean;
    allowSpecialChars?: boolean;
}

interface ValidationRules {
    vehicles: ValidationRule & { defaultDisqualificationMessage: string };
    claims: ValidationRule & { defaultDisqualificationMessage: string };
    vehicleYear: ValidationRule & { defaultErrorMessage: string };
    phoneNumber: Omit<ValidationRule, 'min' | 'max'> & { minLength: number; maxLength: number; allowInternational: boolean };
    name: Omit<ValidationRule, 'min' | 'max'> & { minLength: number; maxLength: number; allowSpecialChars: boolean };
}

interface TransferConfig {
    defaultNumber: string;
    defaultAgentName: string;
    timeout: number;
    maxAttempts: number;
    retryDelay: number;
    warmTransferEnabled: boolean;
    coldTransferEnabled: boolean;
}

interface CampaignConfig {
    maxCampaigns: number;
    defaultCampaignId: number;
    enableCustomScripts: boolean;
    scriptCacheTimeout: number;
    maxScriptLength: number;
    enableScriptValidation: boolean;
}

interface PerformanceConfig {
    maxConcurrentCalls: number;
    callQueueSize: number;
    maxCallDuration: number;
    enableCallRecording: boolean;
    enableCallTranscription: boolean;
    enableMetrics: boolean;
}

interface RetryConfig {
    maxRetries: number;
    retryDelay: number;
    backoffMultiplier: number;
    maxRetryDelay: number;
    enableExponentialBackoff: boolean;
}

interface SecurityConfig {
    enableRecordingConfirmation: boolean;
    recordingConfirmationMessage?: string;
    vocabularyRestrictions: string[];
    enableContentFiltering: boolean;
    maxCallsPerNumber: number;
    callRateLimitWindow: number;
}

interface ValidationResult {
    valid: boolean;
    error?: string;
    disqualify?: boolean;
}

interface CallHistoryEntry {
    phoneNumber: string;
    timestamp: number;
}

type ConfigSection = 'timeouts' | 'validation' | 'transfer' | 'campaign' | 'performance' | 'retry' | 'security';

/**
 * Business Configuration Manager
 * Handles business logic constants, validation rules, and operational parameters
 */
export class BusinessConfigManager {
    private callTimeouts: CallTimeouts;
    private validationRules: ValidationRules;
    private transferConfig: TransferConfig;
    private campaignConfig: CampaignConfig;
    private performanceConfig: PerformanceConfig;
    private retryConfig: RetryConfig;
    private securityConfig: SecurityConfig;

    constructor() {
        // Call timeout configurations
        this.callTimeouts = {
            default: getConfigValue('business.callTimeouts.default', 30) as number,
            intro: getConfigValue('business.callTimeouts.intro', 4) as number,
            response: getConfigValue('business.callTimeouts.response', 10) as number,
            transfer: getConfigValue('business.transfer.transferTimeout', 30) as number,
            recording: parseInt(process.env.RECORDING_TIMEOUT || '300', 10),
            silence: parseInt(process.env.SILENCE_TIMEOUT || '5', 10)
        };

        // Validation rules
        this.validationRules = {
            vehicles: {
                min: 1,
                max: getConfigValue('business.validation.maxVehicles', 9) as number,
                defaultDisqualificationMessage: 'Invalid number of vehicles'
            },
            claims: {
                min: 0,
                max: getConfigValue('business.validation.maxClaims', 3) as number,
                defaultDisqualificationMessage: 'Too many claims in the last 3 years'
            },
            vehicleYear: {
                min: getConfigValue('business.validation.minVehicleYear', 1900) as number,
                max: getConfigValue('business.validation.maxVehicleYear', 2027) as number,
                defaultErrorMessage: 'Please provide a valid 4-digit year'
            },
            phoneNumber: {
                minLength: parseInt(process.env.PHONE_MIN_LENGTH || '10', 10),
                maxLength: parseInt(process.env.PHONE_MAX_LENGTH || '15', 10),
                allowInternational: process.env.ALLOW_INTERNATIONAL_NUMBERS === 'true'
            },
            name: {
                minLength: parseInt(process.env.NAME_MIN_LENGTH || '2', 10),
                maxLength: parseInt(process.env.NAME_MAX_LENGTH || '50', 10),
                allowSpecialChars: process.env.ALLOW_SPECIAL_CHARS_IN_NAME === 'true'
            }
        };

        // Transfer configurations
        this.transferConfig = {
            defaultNumber: getConfigValue('business.transfer.defaultTransferNumber', '************') as string,
            defaultAgentName: getConfigValue('business.transfer.defaultAgentName', 'Sarah Johnson') as string,
            timeout: getConfigValue('business.transfer.transferTimeout', 30) as number,
            maxAttempts: parseInt(process.env.TRANSFER_MAX_ATTEMPTS || '3', 10),
            retryDelay: parseInt(process.env.TRANSFER_RETRY_DELAY || '5', 10),
            warmTransferEnabled: process.env.ENABLE_WARM_TRANSFER === 'true',
            coldTransferEnabled: process.env.ENABLE_COLD_TRANSFER !== 'false'
        };

        // Campaign-specific configurations
        this.campaignConfig = {
            maxCampaigns: getConfigValue('campaigns.totalCampaigns', 6) as number,
            defaultCampaignId: getConfigValue('campaigns.defaultCampaignId', 1) as number,
            enableCustomScripts: getConfigValue('campaigns.enableCustomScripts', false) as boolean,
            scriptCacheTimeout: getConfigValue('campaigns.scriptCacheTimeout', 300) as number,
            maxScriptLength: parseInt(process.env.MAX_SCRIPT_LENGTH || '10000', 10),
            enableScriptValidation: process.env.ENABLE_SCRIPT_VALIDATION !== 'false'
        };

        // Performance and limits
        this.performanceConfig = {
            maxConcurrentCalls: getConfigValue('performance.maxConcurrentCalls', 100) as number,
            callQueueSize: parseInt(process.env.CALL_QUEUE_SIZE || '50', 10),
            maxCallDuration: parseInt(process.env.MAX_CALL_DURATION || '1800', 10), // 30 minutes
            enableCallRecording: process.env.ENABLE_CALL_RECORDING !== 'false',
            enableCallTranscription: getConfigValue('transcription.enabled', true) as boolean,
            enableMetrics: getConfigValue('performance.enableMetrics', true) as boolean
        };

        // Retry and error handling
        this.retryConfig = {
            maxRetries: parseInt(process.env.MAX_RETRIES || '3', 10),
            retryDelay: parseInt(process.env.RETRY_DELAY || '1000', 10),
            backoffMultiplier: parseFloat(process.env.BACKOFF_MULTIPLIER || '2'),
            maxRetryDelay: parseInt(process.env.MAX_RETRY_DELAY || '30000', 10),
            enableExponentialBackoff: process.env.ENABLE_EXPONENTIAL_BACKOFF !== 'false'
        };

        // Security and compliance
        this.securityConfig = {
            enableRecordingConfirmation: getConfigValue('security.enableRecordingConfirmation', true) as boolean,
            recordingConfirmationMessage: getConfigValue('security.recordingConfirmationMessage') as string | undefined,
            vocabularyRestrictions: getConfigValue('security.vocabularyRestrictions', []) as string[],
            enableContentFiltering: process.env.ENABLE_CONTENT_FILTERING === 'true',
            maxCallsPerNumber: parseInt(process.env.MAX_CALLS_PER_NUMBER || '10', 10),
            callRateLimitWindow: parseInt(process.env.CALL_RATE_LIMIT_WINDOW || '3600', 10) // 1 hour
        };
    }

    /**
     * Get call timeout for specific type
     */
    getCallTimeout(type: keyof CallTimeouts = 'default'): number {
        return this.callTimeouts[type] || this.callTimeouts.default;
    }

    /**
     * Validate vehicle count
     */
    validateVehicleCount(count: string | number): ValidationResult {
        const rules = this.validationRules.vehicles;
        const numCount = parseInt(String(count), 10);
        
        if (isNaN(numCount) || numCount < rules.min || numCount > rules.max) {
            return {
                valid: false,
                error: `Vehicle count must be between ${rules.min} and ${rules.max}`,
                disqualify: numCount > rules.max
            };
        }
        
        return { valid: true };
    }

    /**
     * Validate claims count
     */
    validateClaimsCount(count: string | number): ValidationResult {
        const rules = this.validationRules.claims;
        const numCount = parseInt(String(count), 10);
        
        if (isNaN(numCount) || numCount < rules.min) {
            return {
                valid: false,
                error: `Claims count must be ${rules.min} or greater`
            };
        }
        
        if (numCount > rules.max) {
            return {
                valid: false,
                error: rules.defaultDisqualificationMessage,
                disqualify: true
            };
        }
        
        return { valid: true };
    }

    /**
     * Validate vehicle year
     */
    validateVehicleYear(year: string | number): ValidationResult {
        const rules = this.validationRules.vehicleYear;
        const numYear = parseInt(String(year), 10);
        
        if (isNaN(numYear) || numYear < rules.min || numYear > rules.max) {
            return {
                valid: false,
                error: `${rules.defaultErrorMessage} (${rules.min}-${rules.max})`
            };
        }
        
        return { valid: true };
    }

    /**
     * Validate phone number
     */
    validatePhoneNumber(phoneNumber: string): ValidationResult {
        const rules = this.validationRules.phoneNumber;
        
        if (!phoneNumber || typeof phoneNumber !== 'string') {
            return { valid: false, error: 'Phone number is required' };
        }
        
        // Remove non-digit characters for length check
        const digitsOnly = phoneNumber.replace(/\D/g, '');
        
        if (digitsOnly.length < rules.minLength || digitsOnly.length > rules.maxLength) {
            return {
                valid: false,
                error: `Phone number must be ${rules.minLength}-${rules.maxLength} digits`
            };
        }
        
        // Check international format if not allowed
        if (!rules.allowInternational && phoneNumber.startsWith('+')) {
            return { valid: false, error: 'International numbers not allowed' };
        }
        
        return { valid: true };
    }

    /**
     * Validate name
     */
    validateName(name: string): ValidationResult {
        const rules = this.validationRules.name;
        
        if (!name || typeof name !== 'string') {
            return { valid: false, error: 'Name is required' };
        }
        
        const trimmedName = name.trim();
        
        if (trimmedName.length < rules.minLength || trimmedName.length > rules.maxLength) {
            return {
                valid: false,
                error: `Name must be ${rules.minLength}-${rules.maxLength} characters`
            };
        }
        
        // Check for special characters if not allowed
        if (!rules.allowSpecialChars && /[^a-zA-Z\s]/.test(trimmedName)) {
            return { valid: false, error: 'Name can only contain letters and spaces' };
        }
        
        return { valid: true };
    }

    /**
     * Get transfer configuration
     */
    getTransferConfig(campaignId: number | null = null): TransferConfig {
        const config = { ...this.transferConfig };
        
        // Override with campaign-specific settings if available
        if (campaignId) {
            const campaignTransferNumber = process.env[`TRANSFER_NUMBER_CAMPAIGN_${campaignId}`];
            const campaignAgentName = process.env[`AGENT_NAME_CAMPAIGN_${campaignId}`];
            
            if (campaignTransferNumber) config.defaultNumber = campaignTransferNumber;
            if (campaignAgentName) config.defaultAgentName = campaignAgentName;
        }
        
        return config;
    }

    /**
     * Check if call should be rate limited
     */
    shouldRateLimit(phoneNumber: string, callHistory: CallHistoryEntry[] = []): boolean {
        const config = this.securityConfig;
        const windowStart = Date.now() - (config.callRateLimitWindow * 1000);
        
        const recentCalls = callHistory.filter(call => 
            call.phoneNumber === phoneNumber && 
            call.timestamp > windowStart
        );
        
        return recentCalls.length >= config.maxCallsPerNumber;
    }

    /**
     * Get retry configuration for operation
     */
    getRetryConfig(operation: 'default' | 'api_call' | 'database' | 'transfer' = 'default'): RetryConfig {
        const config = { ...this.retryConfig };
        
        // Operation-specific overrides
        const operationOverrides: Record<string, Partial<RetryConfig>> = {
            'api_call': {
                maxRetries: parseInt(process.env.API_CALL_MAX_RETRIES || String(config.maxRetries), 10),
                retryDelay: parseInt(process.env.API_CALL_RETRY_DELAY || String(config.retryDelay), 10)
            },
            'database': {
                maxRetries: parseInt(process.env.DB_MAX_RETRIES || String(config.maxRetries), 10),
                retryDelay: parseInt(process.env.DB_RETRY_DELAY || String(config.retryDelay), 10)
            },
            'transfer': {
                maxRetries: this.transferConfig.maxAttempts,
                retryDelay: this.transferConfig.retryDelay * 1000
            }
        };
        
        return { ...config, ...operationOverrides[operation] };
    }

    /**
     * Get performance limits
     */
    getPerformanceLimits(): PerformanceConfig {
        return { ...this.performanceConfig };
    }

    /**
     * Update business configuration at runtime
     */
    updateBusinessConfig(section: ConfigSection, updates: any): boolean {
        const sections: Record<ConfigSection, keyof BusinessConfigManager> = {
            'timeouts': 'callTimeouts',
            'validation': 'validationRules',
            'transfer': 'transferConfig',
            'campaign': 'campaignConfig',
            'performance': 'performanceConfig',
            'retry': 'retryConfig',
            'security': 'securityConfig'
        };
        
        const configSection = sections[section];
        if (configSection && this[configSection]) {
            (this as any)[configSection] = { ...(this as any)[configSection], ...updates };
            return true;
        }
        
        return false;
    }

    /**
     * Get configuration summary
     */
    getConfigSummary() {
        return {
            callTimeouts: Object.keys(this.callTimeouts),
            validationRules: Object.keys(this.validationRules),
            transferConfig: {
                defaultNumber: this.transferConfig.defaultNumber,
                defaultAgentName: this.transferConfig.defaultAgentName,
                warmTransferEnabled: this.transferConfig.warmTransferEnabled
            },
            performanceLimits: {
                maxConcurrentCalls: this.performanceConfig.maxConcurrentCalls,
                maxCallDuration: this.performanceConfig.maxCallDuration
            },
            securityFeatures: {
                recordingConfirmation: this.securityConfig.enableRecordingConfirmation,
                contentFiltering: this.securityConfig.enableContentFiltering,
                rateLimiting: this.securityConfig.maxCallsPerNumber > 0
            }
        };
    }
}

// Export singleton instance
export const businessConfigManager = new BusinessConfigManager();

// Export utility functions
export function getCallTimeout(type: keyof CallTimeouts = 'default'): number {
    return businessConfigManager.getCallTimeout(type);
}

export function validateVehicleCount(count: string | number): ValidationResult {
    return businessConfigManager.validateVehicleCount(count);
}

export function validateClaimsCount(count: string | number): ValidationResult {
    return businessConfigManager.validateClaimsCount(count);
}

export function validateVehicleYear(year: string | number): ValidationResult {
    return businessConfigManager.validateVehicleYear(year);
}

export function getTransferConfig(campaignId: number | null = null): TransferConfig {
    return businessConfigManager.getTransferConfig(campaignId);
}

export function shouldRateLimit(phoneNumber: string, callHistory: CallHistoryEntry[] = []): boolean {
    return businessConfigManager.shouldRateLimit(phoneNumber, callHistory);
}