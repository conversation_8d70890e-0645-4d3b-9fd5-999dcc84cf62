// Comprehensive Configuration System for Twilio Gemini Project
// Centralizes all configuration with environment variable support and validation

import dotenv from '../utils/dotenv-stub';
import path from 'path';
import { fileURLToPath } from 'url';
import { configLogger as logger } from '../utils/logger';
import type { AppConfig } from '../types/global';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config();

// Provide sensible defaults during test runs so configuration validation passes
if (process.env.NODE_ENV === 'test') {
    process.env.GEMINI_API_KEY ??= 'test-api-key';
    process.env.TWILIO_ACCOUNT_SID ??= 'ACXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX';
    process.env.TWILIO_AUTH_TOKEN ??= 'test-auth-token';
    process.env.PUBLIC_URL ??= 'http://localhost:3101';
}

/**
 * Configuration validation helper
 */
class ConfigValidator {
    static validateRequired(value: string | undefined, name: string): string {
        if (!value) {
            throw new Error(`Required configuration missing: ${name}`);
        }
        return value;
    }

    static validateUrl(value: string | undefined, name: string, required: boolean = false): string | undefined {
        if (!value && required) {
            throw new Error(`Required URL configuration missing: ${name}`);
        }
        if (value && !value.match(/^https?:\/\/.+/)) {
            throw new Error(`Invalid URL format for ${name}: ${value}`);
        }
        return value;
    }

    static validatePort(value: string | undefined, name: string, defaultValue: number = 3000): number {
        const port = parseInt(value || '') || defaultValue;
        if (port < 1 || port > 65535) {
            throw new Error(`Invalid port for ${name}: ${port}`);
        }
        return port;
    }

    static validateEnum(value: string | undefined, validValues: string[], name: string, defaultValue: string | null = null): string {
        if (!value && defaultValue) return defaultValue;
        if (!value || !validValues.includes(value)) {
            throw new Error(`Invalid value for ${name}: ${value}. Valid values: ${validValues.join(', ')}`);
        }
        return value;
    }

    static validateNumber(value: string | undefined, name: string, min: number | null = null, max: number | null = null, defaultValue: number | null = null): number {
        if (!value && defaultValue !== null) return defaultValue;
        const num = parseFloat(value || '');
        if (isNaN(num)) {
            throw new Error(`Invalid number for ${name}: ${value}`);
        }
        if (min !== null && num < min) {
            throw new Error(`${name} must be >= ${min}, got ${num}`);
        }
        if (max !== null && num > max) {
            throw new Error(`${name} must be <= ${max}, got ${num}`);
        }
        return num;
    }
}

/**
 * Main configuration object
 */
export const config: AppConfig = {
    // Environment
    environment: {
        nodeEnv: process.env.NODE_ENV || 'development',
        isDevelopment: (process.env.NODE_ENV || 'development') === 'development',
        isProduction: process.env.NODE_ENV === 'production',
        debugLevel: process.env.DEBUG_LEVEL || 'info',
        enableDetailedLogging: process.env.ENABLE_DETAILED_LOGGING === 'true'
    },

    // Server configuration
    server: {
        port: ConfigValidator.validatePort(process.env.PORT, 'PORT', 3101),
        publicUrl: ConfigValidator.validateUrl(
            process.env.PUBLIC_URL || `http://localhost:${process.env.TWILIO_GEMINI_BACKEND_PORT || process.env.PORT || 3101}`,
            'PUBLIC_URL',
            true
        ),
        corsOrigin: process.env.CORS_ORIGIN || process.env.PUBLIC_URL || '*',
        frontendUrl: process.env.FRONTEND_URL || 'http://localhost:3000'
    },

    // API Keys and Authentication
    auth: {
        gemini: {
            apiKey: ConfigValidator.validateRequired(process.env.GEMINI_API_KEY, 'GEMINI_API_KEY')
        },
        openai: {
            apiKey: process.env.OPENAI_API_KEY,
            apiUrl: process.env.OPENAI_API_URL || 'https://api.openai.com'
        },
        twilio: {
            accountSid: ConfigValidator.validateRequired(process.env.TWILIO_ACCOUNT_SID, 'TWILIO_ACCOUNT_SID'),
            authToken: ConfigValidator.validateRequired(process.env.TWILIO_AUTH_TOKEN, 'TWILIO_AUTH_TOKEN')
        },
        deepgram: {
            apiKey: process.env.DEEPGRAM_API_KEY
        },
        ngrok: {
            authToken: process.env.NGROK_AUTHTOKEN
        }
    },

    // Twilio Configuration
    twilio: {
        phoneNumbers: {
            default: process.env.TWILIO_PHONE_NUMBER || process.env.TWILIO_PHONE_NUMBER_US,
            us: process.env.TWILIO_PHONE_NUMBER_US,
            cz: process.env.TWILIO_PHONE_NUMBER_CZ,
            es: process.env.TWILIO_PHONE_NUMBER_ES
        },
        defaultPhoneNumber: process.env.DEFAULT_PHONE_NUMBER,
        webhooks: {
            voiceUrl: process.env.TWILIO_VOICE_URL,
            statusCallbackUrl: process.env.TWILIO_STATUS_CALLBACK_URL,
            recordingStatusCallbackUrl: process.env.TWILIO_RECORDING_STATUS_CALLBACK_URL
        }
    },

    // AI Model Configuration
    ai: {
        gemini: {
            defaultModel: process.env.GEMINI_DEFAULT_MODEL || 'gemini-2.5-flash-preview-native-audio-dialog',
            availableModels: (process.env.GEMINI_AVAILABLE_MODELS || 'gemini-2.5-flash-preview-native-audio-dialog,gemini-2.0-flash-live-001').split(',').map(m => m.trim()),
            modelSelectionEnabled: process.env.GEMINI_MODEL_SELECTION_ENABLED === 'true',
            defaultVoice: process.env.GEMINI_DEFAULT_VOICE || 'Kore',
            voiceSelectionEnabled: process.env.GEMINI_VOICE_SELECTION_ENABLED === 'true',
            outputRate: ConfigValidator.validateNumber(process.env.GEMINI_OUTPUT_RATE, 'GEMINI_OUTPUT_RATE', 8000, 48000, 24000),
            temperature: ConfigValidator.validateNumber(process.env.GEMINI_TEMPERATURE, 'GEMINI_TEMPERATURE', 0, 2, 0.7),
            topP: ConfigValidator.validateNumber(process.env.GEMINI_TOP_P, 'GEMINI_TOP_P', 0, 1, 0.9),
            topK: ConfigValidator.validateNumber(process.env.GEMINI_TOP_K, 'GEMINI_TOP_K', 1, 100, 40),
            voices: {
                Aoede: process.env.VOICE_AOEDE || 'Aoede, female, bright neutral narrator',
                Puck: process.env.VOICE_PUCK || 'Puck, male, lively higher tenor',
                Charon: process.env.VOICE_CHARON || 'Charon, male, deep warm baritone',
                Kore: process.env.VOICE_KORE || 'Kore, female, soft alto empathetic',
                Fenrir: process.env.VOICE_FENRIR || 'Fenrir, male, assertive mid-range',
                Leda: process.env.VOICE_LEDA || 'Leda, female, clear RP-style announcer',
                Orus: process.env.VOICE_ORUS || 'Orus, male, relaxed breathy tenor',
                Zephyr: process.env.VOICE_ZEPHYR || 'Zephyr, female, airy youthful soprano'
            }
        },
        openai: {
            model: process.env.OPENAI_MODEL || 'gpt-4o-mini-realtime-preview',
            chatModel: process.env.OPENAI_CHAT_MODEL || 'gpt-4o-mini',
            voice: process.env.OPENAI_VOICE || 'shimmer',
            temperature: ConfigValidator.validateNumber(process.env.OPENAI_TEMPERATURE, 'OPENAI_TEMPERATURE', 0, 2, 1.1)
        }
    },

    // Audio Configuration
    audio: {
        inputFormat: process.env.INPUT_AUDIO_FORMAT || 'g711_ulaw',
        outputFormat: process.env.OUTPUT_AUDIO_FORMAT || 'g711_ulaw',
        sampleRate: ConfigValidator.validateNumber(process.env.SAMPLE_RATE, 'SAMPLE_RATE', 8000, 48000, 16000),
        twilioSampleRate: ConfigValidator.validateNumber(process.env.TWILIO_SAMPLE_RATE, 'TWILIO_SAMPLE_RATE', 8000, 48000, 8000),
        greetingAudioUrl: process.env.GREETING_AUDIO_URL
    },

    // WebSocket Configuration
    websocket: {
        protocol: process.env.WS_PROTOCOL || 'wss',
        url: process.env.WEBSOCKET_URL
    },

    // Transcription Configuration
    transcription: {
        enabled: process.env.ENABLE_TRANSCRIPTION === 'true',
        model: process.env.TRANSCRIPTION_MODEL || 'whisper-1',
        language: process.env.TRANSCRIPTION_LANGUAGE || 'en',
        responseFormat: process.env.TRANSCRIPTION_RESPONSE_FORMAT || 'json'
    },

    // Campaign Configuration
    campaigns: {
        scriptsPath: process.env.CAMPAIGN_SCRIPTS_PATH || path.join(__dirname, '../../call-center-frontend/public'),
        totalCampaigns: ConfigValidator.validateNumber(process.env.TOTAL_CAMPAIGNS, 'TOTAL_CAMPAIGNS', 1, 100, 6),
        defaultCampaignId: ConfigValidator.validateNumber(process.env.DEFAULT_CAMPAIGN_ID, 'DEFAULT_CAMPAIGN_ID', 1, 100, 1),
        enableCustomScripts: process.env.ENABLE_CUSTOM_SCRIPTS === 'true',
        scriptCacheTimeout: ConfigValidator.validateNumber(process.env.SCRIPT_CACHE_TIMEOUT, 'SCRIPT_CACHE_TIMEOUT', 0, 86400, 300)
    },

    // Language and Localization
    localization: {
        defaultLanguage: process.env.DEFAULT_LANGUAGE || 'en',
        supportedLanguages: (process.env.SUPPORTED_LANGUAGES || 'en,es,cz').split(',').map(l => l.trim()),
        enableMultiLanguage: process.env.ENABLE_MULTI_LANGUAGE === 'true',
        fallbackLanguage: process.env.FALLBACK_LANGUAGE || 'en'
    },

    // Voice Configuration
    voices: {
        defaultVoiceMapping: {
            en: {
                incoming: process.env.VOICE_EN_INCOMING || 'Kore',
                outbound: process.env.VOICE_EN_OUTBOUND || 'Aoede'
            },
            es: {
                incoming: process.env.VOICE_ES_INCOMING || 'Kore',
                outbound: process.env.VOICE_ES_OUTBOUND || 'Puck'
            },
            cz: {
                incoming: process.env.VOICE_CZ_INCOMING || 'Charon',
                outbound: process.env.VOICE_CZ_OUTBOUND || 'Fenrir'
            }
        },
        enableVoiceSelection: process.env.ENABLE_VOICE_SELECTION === 'true'
    },

    // Business Logic Constants
    business: {
        callTimeouts: {
            default: ConfigValidator.validateNumber(process.env.CALL_TIMEOUT_DEFAULT, 'CALL_TIMEOUT_DEFAULT', 1, 3600, 30),
            intro: ConfigValidator.validateNumber(process.env.CALL_TIMEOUT_INTRO, 'CALL_TIMEOUT_INTRO', 1, 60, 4),
            response: ConfigValidator.validateNumber(process.env.CALL_TIMEOUT_RESPONSE, 'CALL_TIMEOUT_RESPONSE', 1, 60, 10)
        },
        validation: {
            maxVehicles: ConfigValidator.validateNumber(process.env.MAX_VEHICLES, 'MAX_VEHICLES', 1, 50, 9),
            maxClaims: ConfigValidator.validateNumber(process.env.MAX_CLAIMS, 'MAX_CLAIMS', 0, 20, 3),
            minVehicleYear: ConfigValidator.validateNumber(process.env.MIN_VEHICLE_YEAR, 'MIN_VEHICLE_YEAR', 1900, 2030, 1900),
            maxVehicleYear: ConfigValidator.validateNumber(process.env.MAX_VEHICLE_YEAR, 'MAX_VEHICLE_YEAR', 1900, 2030, 2027)
        },
        transfer: {
            defaultTransferNumber: process.env.DEFAULT_TRANSFER_NUMBER || '555-123-4567',
            defaultAgentName: process.env.DEFAULT_AGENT_NAME || 'Sarah Johnson',
            transferTimeout: ConfigValidator.validateNumber(process.env.TRANSFER_TIMEOUT, 'TRANSFER_TIMEOUT', 1, 300, 30)
        }
    },

    // System Messages and Prompts
    prompts: {
        summaryGeneration: process.env.SUMMARY_GENERATION_PROMPT || 'Report campaign related result and important context or follow up.',
        aiPrepareOutbound: process.env.AI_PREPARE_MESSAGE_OUTBOUND || '',
        aiPrepareIncoming: process.env.AI_PREPARE_MESSAGE_INCOMING || 'Hello, thank you for calling. How may I assist you today?',
        systemMessageBio: process.env.SYSTEM_MESSAGE_BIO || '',
        systemMessageVoicePersonality: process.env.SYSTEM_MESSAGE_VOICE_PERSONALITY || '',
        systemMessageVoiceSpeed: process.env.SYSTEM_MESSAGE_VOICE_SPEED || ''
    },

    // Security and Safety
    security: {
        vocabularyRestrictions: (process.env.VOCABULARY_RESTRICTIONS || 'AI,artificial intelligence,machine learning,algorithm,model,training data,neural network,politics,religion,sensitive topics,controversial topics').split(',').map(w => w.trim()),
        enableRecordingConfirmation: process.env.ENABLE_RECORDING_CONFIRMATION === 'true',
        recordingConfirmationMessage: process.env.RECORDING_CONFIRMATION_MESSAGE || 'This call is recorded for quality assurance. Is it okay to continue?'
    },

    // Performance and Caching
    performance: {
        enableCaching: process.env.ENABLE_CACHING === 'true',
        cacheTimeout: ConfigValidator.validateNumber(process.env.CACHE_TIMEOUT, 'CACHE_TIMEOUT', 1, 86400, 300),
        maxConcurrentCalls: ConfigValidator.validateNumber(process.env.MAX_CONCURRENT_CALLS, 'MAX_CONCURRENT_CALLS', 1, 1000, 100),
        enableMetrics: process.env.ENABLE_METRICS === 'true'
    }
};

/**
 * Comprehensive configuration validation function
 */
export function validateConfig(): boolean {
    const errors = [];
    const warnings = [];

    try {
        // Required configurations
        try {
            ConfigValidator.validateRequired(config.auth.gemini.apiKey, 'GEMINI_API_KEY');
        } catch (e) { errors.push(e.message); }

        try {
            ConfigValidator.validateRequired(config.auth.twilio.accountSid, 'TWILIO_ACCOUNT_SID');
        } catch (e) { errors.push(e.message); }

        try {
            ConfigValidator.validateRequired(config.auth.twilio.authToken, 'TWILIO_AUTH_TOKEN');
        } catch (e) { errors.push(e.message); }

        try {
            ConfigValidator.validateUrl(config.server.publicUrl, 'PUBLIC_URL', true);
        } catch (e) { errors.push(e.message); }

        // Optional but recommended configurations
        if (!config.auth.deepgram.apiKey) {
            warnings.push('DEEPGRAM_API_KEY not set - transcription features will be limited');
        }

        if (!config.auth.openai.apiKey) {
            warnings.push('OPENAI_API_KEY not set - OpenAI features will be unavailable');
        }

        // Validate numeric ranges
        if (config.server.port < 1 || config.server.port > 65535) {
            errors.push(`Invalid PORT: ${config.server.port} (must be 1-65535)`);
        }

        if (config.ai.gemini.temperature < 0 || config.ai.gemini.temperature > 2) {
            errors.push(`Invalid GEMINI_TEMPERATURE: ${config.ai.gemini.temperature} (must be 0-2)`);
        }

        if (config.ai.gemini.topP < 0 || config.ai.gemini.topP > 1) {
            errors.push(`Invalid GEMINI_TOP_P: ${config.ai.gemini.topP} (must be 0-1)`);
        }

        // Validate audio settings
        if (config.audio.sampleRate < 8000 || config.audio.sampleRate > 48000) {
            warnings.push(`Unusual SAMPLE_RATE: ${config.audio.sampleRate} (recommended: 8000-48000)`);
        }

        // Validate language settings
        const supportedLangs = config.localization.supportedLanguages;
        if (!supportedLangs.includes(config.localization.defaultLanguage)) {
            errors.push(`DEFAULT_LANGUAGE '${config.localization.defaultLanguage}' not in SUPPORTED_LANGUAGES`);
        }

        // Validate business logic
        if (config.business.validation.maxVehicles < 1 || config.business.validation.maxVehicles > 50) {
            warnings.push(`Unusual MAX_VEHICLES: ${config.business.validation.maxVehicles} (recommended: 1-50)`);
        }

        // Report results
        if (errors.length > 0) {
            logger.error('Configuration validation failed', {});
            errors.forEach(error => logger.error(error));
            throw new Error(`Configuration validation failed with ${errors.length} error(s)`);
        }

        if (warnings.length > 0) {
            logger.warn('Configuration warnings', {});
            warnings.forEach(warning => logger.warn(warning));
        }

        logger.info('Configuration validation passed');
        if (warnings.length > 0) {
            logger.info(`(${warnings.length} warning(s) - see above)`);
        }

        return true;
    } catch (error) {
        if (errors.length === 0) {
            logger.error('Configuration validation failed', { error: error.message });
            throw error;
        }
        throw error;
    }
}

/**
 * Get configuration value with fallback
 */
export function getConfigValue(path: string, fallback: any = null): any {
    const keys = path.split('.');
    let value = config;

    for (const key of keys) {
        if (value && typeof value === 'object' && key in value) {
            value = value[key];
        } else {
            return fallback;
        }
    }

    return value;
}

/**
 * Get all configuration as a safe object (without sensitive data)
 */
export function getSafeConfig(): any {
    const safeConfig = JSON.parse(JSON.stringify(config));

    // Remove sensitive data
    if (safeConfig.auth) {
        Object.keys(safeConfig.auth).forEach(provider => {
            if (safeConfig.auth[provider].apiKey) {
                safeConfig.auth[provider].apiKey = '***REDACTED***';
            }
            if (safeConfig.auth[provider].authToken) {
                safeConfig.auth[provider].authToken = '***REDACTED***';
            }
        });
    }

    return safeConfig;
}

/**
 * Get configuration summary for debugging
 */
export function getConfigSummary(): any {
    return {
        environment: config.environment.nodeEnv,
        server: {
            port: config.server.port,
            publicUrl: config.server.publicUrl ? 'SET' : 'NOT SET'
        },
        auth: {
            gemini: config.auth.gemini.apiKey ? 'SET' : 'NOT SET',
            twilio: config.auth.twilio.accountSid ? 'SET' : 'NOT SET',
            openai: config.auth.openai.apiKey ? 'SET' : 'NOT SET',
            deepgram: config.auth.deepgram.apiKey ? 'SET' : 'NOT SET'
        },
        ai: {
            defaultModel: config.ai.gemini.defaultModel,
            defaultVoice: config.ai.gemini.defaultVoice,
            availableModels: config.ai.gemini.availableModels.length
        },
        campaigns: {
            totalCampaigns: config.campaigns.totalCampaigns,
            scriptsPath: config.campaigns.scriptsPath ? 'SET' : 'NOT SET',
            enableCustomScripts: config.campaigns.enableCustomScripts
        },
        localization: {
            defaultLanguage: config.localization.defaultLanguage,
            supportedLanguages: config.localization.supportedLanguages,
            enableMultiLanguage: config.localization.enableMultiLanguage
        },
        performance: {
            enableCaching: config.performance.enableCaching,
            maxConcurrentCalls: config.performance.maxConcurrentCalls,
            enableMetrics: config.performance.enableMetrics
        }
    };
}

/**
 * Validate specific configuration section
 */
export function validateConfigSection(section: string): { valid: boolean; error?: string } {
    const validators = {
        auth: () => {
            ConfigValidator.validateRequired(config.auth.gemini.apiKey, 'GEMINI_API_KEY');
            ConfigValidator.validateRequired(config.auth.twilio.accountSid, 'TWILIO_ACCOUNT_SID');
            ConfigValidator.validateRequired(config.auth.twilio.authToken, 'TWILIO_AUTH_TOKEN');
        },
        server: () => {
            ConfigValidator.validateUrl(config.server.publicUrl, 'PUBLIC_URL', true);
            ConfigValidator.validatePort(config.server.port, 'PORT');
        },
        ai: () => {
            ConfigValidator.validateNumber(config.ai.gemini.temperature, 'GEMINI_TEMPERATURE', 0, 2);
            ConfigValidator.validateNumber(config.ai.gemini.topP, 'GEMINI_TOP_P', 0, 1);
            ConfigValidator.validateNumber(config.ai.gemini.topK, 'GEMINI_TOP_K', 1, 100);
        },
        audio: () => {
            ConfigValidator.validateNumber(config.audio.sampleRate, 'SAMPLE_RATE', 8000, 48000);
            ConfigValidator.validateNumber(config.audio.twilioSampleRate, 'TWILIO_SAMPLE_RATE', 8000, 48000);
        }
    };

    if (validators[section]) {
        try {
            validators[section]();
            return { valid: true };
        } catch (error) {
            return { valid: false, error: error.message };
        }
    }

    return { valid: false, error: `Unknown configuration section: ${section}` };
}

/**
 * Export default configuration
 */
export default config;
