// Voice Management System for Gemini
import { config, getConfigValue } from '../config/config';

interface VoiceCharacteristics {
    name: string;
    gender: 'Male' | 'Female';
    characteristics: string;
    pitch: string;
    timbre: string;
    persona: string;
}

interface VoiceInfo extends VoiceCharacteristics {}

interface VoiceValidationResult {
    isValid: boolean;
    voice?: string;
    info?: VoiceInfo;
    mapped?: boolean;
    originalVoice?: string;
    error?: string;
    suggestion?: string;
    availableVoices?: string[];
    voiceMapping?: string[];
}

interface VoiceConfig {
    defaultVoice: string;
    availableVoices: Record<string, VoiceInfo>;
    voiceMapping: Record<string, string>;
    voiceSelectionEnabled: boolean;
    totalVoices: number;
}

export class VoiceManager {
    private defaultVoice: string;
    private voiceSelectionEnabled: boolean;
    private availableVoices: Record<string, VoiceInfo>;
    private voiceMapping: Record<string, string>;

    constructor() {
        this.defaultVoice = getConfigValue('ai.gemini.defaultVoice', '<PERSON><PERSON>') as string;
        this.voiceSelectionEnabled = getConfigValue('ai.gemini.voiceSelectionEnabled', false) as boolean;
        
        // Available Gemini voices with characteristics (Updated June 11, 2025)
        this.availableVoices = {
            'Aoede': {
                name: 'Aoede',
                gender: 'Female',
                characteristics: 'bright, neutral narrator',
                pitch: 'neutral',
                timbre: 'bright',
                persona: 'narrator'
            },
            'Puck': {
                name: 'Puck',
                gender: 'Male',
                characteristics: 'lively, higher tenor',
                pitch: 'higher',
                timbre: 'lively',
                persona: 'energetic'
            },
            'Charon': {
                name: 'Charon',
                gender: 'Male',
                characteristics: 'deep, warm baritone',
                pitch: 'deep',
                timbre: 'warm',
                persona: 'authoritative'
            },
            'Kore': {
                name: 'Kore',
                gender: 'Female',
                characteristics: 'soft alto, empathetic',
                pitch: 'alto',
                timbre: 'soft',
                persona: 'empathetic'
            },
            'Fenrir': {
                name: 'Fenrir',
                gender: 'Male',
                characteristics: 'assertive mid-range',
                pitch: 'mid-range',
                timbre: 'assertive',
                persona: 'confident'
            },
            'Leda': {
                name: 'Leda',
                gender: 'Female',
                characteristics: 'clear RP-style announcer',
                pitch: 'clear',
                timbre: 'professional',
                persona: 'announcer'
            },
            'Orus': {
                name: 'Orus',
                gender: 'Male',
                characteristics: 'relaxed, breathy tenor',
                pitch: 'tenor',
                timbre: 'breathy',
                persona: 'relaxed'
            },
            'Zephyr': {
                name: 'Zephyr',
                gender: 'Female',
                characteristics: 'airy, youthful soprano',
                pitch: 'soprano',
                timbre: 'airy',
                persona: 'youthful'
            }
        };

        // Voice mapping for different languages/accents and compatibility
        this.voiceMapping = {
            // OpenAI voice mappings (for compatibility)
            'shimmer': 'Orus',      // shimmer → Gemini Orus (relaxed, breathy tenor)
            'alloy': 'Puck',        // alloy → Gemini Puck (lively, higher tenor)
            'echo': 'Charon',       // echo → Gemini Charon (deep, warm baritone)
            'fable': 'Kore',        // fable → Gemini Kore (soft alto, empathetic)
            'onyx': 'Fenrir',       // onyx → Gemini Fenrir (assertive mid-range)
            'nova': 'Aoede',        // nova → Gemini Aoede (bright, neutral narrator)

            // Gender-based mappings
            'female': 'Kore',       // Default female → empathetic
            'male': 'Orus',         // Default male → relaxed
            'professional': 'Leda', // Professional → RP-style announcer
            'youthful': 'Zephyr',   // Youthful → airy, youthful soprano
            'authoritative': 'Charon', // Authoritative → deep, warm baritone
            'energetic': 'Puck',    // Energetic → lively, higher tenor
            'empathetic': 'Kore',   // Empathetic → soft alto, empathetic
            'relaxed': 'Orus'       // Relaxed → relaxed, breathy tenor
        };
    }

    /**
     * Get all available voices
     */
    getAvailableVoices(): Record<string, VoiceInfo> {
        return this.availableVoices;
    }

    /**
     * Get default voice
     */
    getDefaultVoice(): string {
        return this.defaultVoice;
    }

    /**
     * Check if voice selection is enabled
     */
    isVoiceSelectionEnabled(): boolean {
        return this.voiceSelectionEnabled;
    }

    /**
     * Get voice mapping
     */
    getVoiceMapping(): Record<string, string> {
        return this.voiceMapping;
    }

    /**
     * Validate and get valid Gemini voice
     */
    getValidGeminiVoice(requestedVoice?: string): string {
        if (!requestedVoice) {
            console.log(`🎤 No voice specified, using default: ${this.defaultVoice} (${this.availableVoices[this.defaultVoice]?.characteristics || 'unknown'})`);
            return this.defaultVoice;
        }

        // Check if it's already a valid Gemini voice
        if (this.availableVoices[requestedVoice]) {
            const voiceInfo = this.availableVoices[requestedVoice];
            console.log(`🎤 Using requested voice: ${requestedVoice} (${voiceInfo.gender}, ${voiceInfo.characteristics})`);
            return requestedVoice;
        }

        // Check if it's an OpenAI voice or other mapping that needs conversion
        if (this.voiceMapping[requestedVoice]) {
            const mappedVoice = this.voiceMapping[requestedVoice];
            const voiceInfo = this.availableVoices[mappedVoice];
            console.log(`🎤 Mapped voice '${requestedVoice}' → '${mappedVoice}' (${voiceInfo.gender}, ${voiceInfo.characteristics})`);
            return mappedVoice;
        }

        // Default fallback
        const defaultInfo = this.availableVoices[this.defaultVoice];
        console.log(`⚠️ Unknown voice '${requestedVoice}', using default: ${this.defaultVoice} (${defaultInfo?.characteristics || 'unknown'})`);
        return this.defaultVoice;
    }

    /**
     * Get voice information
     */
    getVoiceInfo(voiceName: string): VoiceInfo | null {
        return this.availableVoices[voiceName] || null;
    }

    /**
     * Set default voice
     */
    setDefaultVoice(voiceName: string): boolean {
        const validVoice = this.getValidGeminiVoice(voiceName);
        if (validVoice && this.availableVoices[validVoice]) {
            this.defaultVoice = validVoice;
            console.log(`🎤 Default voice changed to: ${validVoice}`);
            return true;
        }
        return false;
    }

    /**
     * Get voice configuration for API response
     */
    getVoiceConfig(): VoiceConfig {
        return {
            defaultVoice: this.defaultVoice,
            availableVoices: this.availableVoices,
            voiceMapping: this.voiceMapping,
            voiceSelectionEnabled: this.voiceSelectionEnabled,
            totalVoices: Object.keys(this.availableVoices).length
        };
    }

    /**
     * Validate voice name
     */
    validateVoice(voiceName: string): VoiceValidationResult {
        if (!voiceName) {
            return {
                isValid: false,
                error: 'Voice name is required',
                suggestion: this.defaultVoice
            };
        }

        if (this.availableVoices[voiceName]) {
            return {
                isValid: true,
                voice: voiceName,
                info: this.availableVoices[voiceName]
            };
        }

        if (this.voiceMapping[voiceName]) {
            const mappedVoice = this.voiceMapping[voiceName];
            return {
                isValid: true,
                voice: mappedVoice,
                mapped: true,
                originalVoice: voiceName,
                info: this.availableVoices[mappedVoice]
            };
        }

        return {
            isValid: false,
            error: `Unknown voice '${voiceName}'`,
            suggestion: this.defaultVoice,
            availableVoices: Object.keys(this.availableVoices),
            voiceMapping: Object.keys(this.voiceMapping)
        };
    }
}

// Create singleton instance
export const voiceManager = new VoiceManager();