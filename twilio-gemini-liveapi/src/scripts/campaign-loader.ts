// Minimal Campaign Script Loader
// This is a placeholder for the actual campaign loader that should be implemented

import { CampaignScript, ScriptInfo } from './campaign-types';
import { logger } from '../utils/logger';
import * as fs from 'fs';
import * as path from 'path';

// Default campaign scripts for fallback
const DEFAULT_CAMPAIGNS: CampaignScript[] = [
  {
    id: 1,
    type: 'outbound',
    language: 'en',
    category: 'sales',
    title: 'Default Outbound Campaign',
    campaign: 'Sales Campaign',
    agentPersona: {
      name: 'Sales Agent',
      voice: '<PERSON><PERSON>',
      model: 'gemini-2.5-flash-preview-native-audio-dialog',
      tone: 'professional',
      humanEmulation: true
    }
  },
  {
    id: 1,
    type: 'incoming',
    language: 'en',
    category: 'support',
    title: 'Default Incoming Campaign',
    campaign: 'Customer Support',
    agentPersona: {
      name: 'Support Agent',
      voice: 'Kore',
      model: 'gemini-2.5-flash-preview-native-audio-dialog',
      tone: 'helpful',
      humanEmulation: true
    }
  }
];

export function loadCampaignScript(
  campaignId: number, 
  type: 'incoming' | 'outbound' = 'outbound',
  useCache: boolean = true
): CampaignScript | null {
  try {
    // Try to load from public directory
    const scriptPath = path.join(process.cwd(), 'public', 'campaigns', type, `campaign-${campaignId}.json`);
    
    if (fs.existsSync(scriptPath)) {
      const scriptContent = fs.readFileSync(scriptPath, 'utf-8');
      const script = JSON.parse(scriptContent) as CampaignScript;
      logger.info(`Loaded campaign script from file: ${scriptPath}`);
      return script;
    }
    
    // Fallback to default
    logger.warn(`Campaign script not found: ${scriptPath}, using default`);
    return DEFAULT_CAMPAIGNS.find(c => c.type === type) || null;
  } catch (error) {
    logger.error('Error loading campaign script:', error instanceof Error ? error : new Error(String(error)));
    return DEFAULT_CAMPAIGNS.find(c => c.type === type) || null;
  }
}

export function getAllCampaigns(): CampaignScript[] {
  const campaigns: CampaignScript[] = [];
  
  try {
    // Load all campaigns from 1-6 for both types
    for (let i = 1; i <= 6; i++) {
      const outbound = loadCampaignScript(i, 'outbound');
      if (outbound) campaigns.push(outbound);
      
      const incoming = loadCampaignScript(i, 'incoming');
      if (incoming) campaigns.push(incoming);
    }
  } catch (error) {
    logger.error('Error loading all campaigns:', error instanceof Error ? error : new Error(String(error)));
  }
  
  return campaigns.length > 0 ? campaigns : DEFAULT_CAMPAIGNS;
}

export function formatCampaignScript(script: CampaignScript): string {
  if (!script) return '';
  
  let formatted = '';
  
  if (script.title) {
    formatted += `Campaign: ${script.title}\n`;
  }
  
  if (script.campaign) {
    formatted += `\n${script.campaign}\n`;
  }
  
  if (script.objectives) {
    formatted += `\nObjectives:\n${script.objectives.join('\n')}\n`;
  }
  
  return formatted;
}

// Placeholder functions for compatibility
export function getIncomingCallScript(scriptId: string): CampaignScript | null {
  const id = parseInt(scriptId) || 1;
  return loadCampaignScript(id, 'incoming');
}

export function listIncomingCallScripts(): ScriptInfo[] {
  const scripts: ScriptInfo[] = [];
  for (let i = 1; i <= 6; i++) {
    scripts.push({
      id: `incoming-${i}`,
      name: `Incoming Campaign ${i}`,
      description: `Customer support campaign ${i}`,
      type: 'incoming',
      category: 'support'
    });
  }
  return scripts;
}

export function setIncomingCallScript(scriptId: string): boolean {
  // This would typically update a configuration
  logger.info(`Setting incoming call script to: ${scriptId}`);
  return true;
}

export function getCurrentIncomingScript(): string {
  return 'incoming-1';
}

export function createCustomIncomingScript(scriptData: any): boolean {
  logger.warn('Custom incoming scripts not supported - use campaigns 1-6');
  return false;
}

// Incoming scenario functions
export function getIncomingScenario(scenarioId: string): CampaignScript | null {
  const id = parseInt(scenarioId.replace(/\D/g, '')) || 1;
  return loadCampaignScript(id, 'incoming');
}

export function listIncomingScenarios(): ScriptInfo[] {
  return listIncomingCallScripts();
}

export function setActiveIncomingScenario(scenarioId: string): boolean {
  return setIncomingCallScript(scenarioId);
}

export function getCurrentIncomingScenario(): string {
  return getCurrentIncomingScript();
}