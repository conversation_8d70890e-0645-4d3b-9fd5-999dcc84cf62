import { ConnectionData } from '../types/global';

// Bounded Map for memory safety
class BoundedMap<K, V> extends Map<K, V> {
    constructor(private maxSize: number = 1000) {
        super();
    }

    set(key: K, value: V): this {
        if (this.size >= this.maxSize && !this.has(key)) {
            const firstKey = this.keys().next().value;
            this.delete(firstKey);
            console.log(`🧹 ContextManager BoundedMap: Removed oldest entry ${firstKey}`);
        }
        return super.set(key, value);
    }
}

// Type definitions for context storage
interface SessionConfig {
    aiInstructions: string;
    voice: string;
    model: string;
    targetName?: string;
    targetPhoneNumber?: string;
    isIncomingCall: boolean;
    scriptId: string | null;
    originalAIInstructions?: string;
}

interface ConversationState {
    isSessionActive: boolean;
    summaryRequested: boolean;
    summaryText: string;
    summaryReceived: boolean;
    conversationLog: Array<{
        role: 'user' | 'assistant';
        content: string;
        timestamp: number;
        messageId?: string;
    }>;
    fullTranscript: Array<{
        role: 'user' | 'assistant';
        content: string;
        timestamp: number;
        confidence?: number;
    }>;
    speechTranscript: Array<{
        text: string;
        timestamp: number;
        confidence: number;
    }>;
    lastAIResponse: number;
    responseTimeouts: number;
    connectionQuality: 'good' | 'fair' | 'poor';
}

interface ConnectionState {
    streamSid?: string;
    lastActivity: number;
    audioBufferState: any | null;
    sessionStartTime: number;
    keepAliveActive: boolean;
}

interface RecoveryInfo {
    lastRecoveryTime: number | null;
    recoveryCount: number;
    wasInterrupted: boolean;
    interruptionReason: string | null;
    interruptionTime: number | null;
    maxRecoveryAttempts: number;
}

export interface SessionContext {
    callSid: string;
    timestamp: number;
    sessionConfig: SessionConfig;
    conversationState: ConversationState;
    connectionState: ConnectionState;
    recoveryInfo: RecoveryInfo;
}

// Context Storage and Recovery System
export class ContextManager {
    public contextStore: BoundedMap<string, SessionContext>;
    private recoveryAttempts: BoundedMap<string, number>;
    private maxRecoveryAttempts: number;
    private contextSaveInterval: number;

    constructor() {
        this.contextStore = new BoundedMap<string, SessionContext>(1000); // Limit to 1000 contexts
        this.recoveryAttempts = new BoundedMap<string, number>(500); // Limit to 500 recovery attempts
        this.maxRecoveryAttempts = 3;
        this.contextSaveInterval = 5000; // Save context every 5 seconds
    }

    // Save session context for recovery
    saveSessionContext(callSid: string, context: any): SessionContext {
        const timestamp = Date.now();
        const sessionContext: SessionContext = {
            callSid,
            timestamp,
            sessionConfig: {
                aiInstructions: context.aiInstructions,
                voice: context.voice,
                model: context.model,
                targetName: context.targetName,
                targetPhoneNumber: context.targetPhoneNumber,
                isIncomingCall: context.isIncomingCall,
                scriptId: context.scriptId || null,
                originalAIInstructions: context.originalAIInstructions || context.aiInstructions
            },
            conversationState: {
                isSessionActive: context.isSessionActive,
                summaryRequested: context.summaryRequested || false,
                summaryText: context.summaryText || '',
                summaryReceived: context.summaryReceived || false,
                conversationLog: context.conversationLog || [],
                fullTranscript: context.fullTranscript || [],
                speechTranscript: context.speechTranscript || [], // For Deepgram transcriptions
                lastAIResponse: context.lastAIResponse || timestamp,
                responseTimeouts: context.responseTimeouts || 0,
                connectionQuality: context.connectionQuality || 'good'
            },
            connectionState: {
                streamSid: context.streamSid,
                lastActivity: timestamp,
                audioBufferState: context.audioBufferState || null,
                sessionStartTime: context.sessionStartTime || timestamp,
                keepAliveActive: context.keepAliveActive || false
            },
            recoveryInfo: {
                lastRecoveryTime: null,
                recoveryCount: this.recoveryAttempts.get(callSid) || 0,
                wasInterrupted: false,
                interruptionReason: null,
                interruptionTime: null,
                maxRecoveryAttempts: this.maxRecoveryAttempts
            }
        };

        this.contextStore.set(callSid, sessionContext);
        console.log(`💾 [${callSid}] Session context saved at ${new Date(timestamp).toISOString()}`);
        return sessionContext;
    }

    // Retrieve session context for recovery
    getSessionContext(callSid: string): SessionContext | undefined {
        return this.contextStore.get(callSid);
    }

    // Mark session as interrupted and needing recovery
    markSessionInterrupted(callSid: string, reason: string = 'unknown'): void {
        const context = this.contextStore.get(callSid);
        if (context) {
            context.recoveryInfo.wasInterrupted = true;
            context.recoveryInfo.interruptionReason = reason;
            context.recoveryInfo.interruptionTime = Date.now();
            this.contextStore.set(callSid, context);
            console.log(`⚠️ [${callSid}] Session marked as interrupted: ${reason}`);
        }
    }

    // Check if session can be recovered
    canRecover(callSid: string): boolean {
        const context = this.contextStore.get(callSid);
        if (!context) {
            return false;
        }

        const recoveryCount = this.recoveryAttempts.get(callSid) || 0;
        const timeSinceLastActivity = Date.now() - context.connectionState.lastActivity;
        const maxRecoveryTime = 300000; // 5 minutes

        return recoveryCount < this.maxRecoveryAttempts && timeSinceLastActivity < maxRecoveryTime;
    }

    // Increment recovery attempt counter
    incrementRecoveryAttempt(callSid: string): number {
        const current = this.recoveryAttempts.get(callSid) || 0;
        this.recoveryAttempts.set(callSid, current + 1);
        return current + 1;
    }

    // Clean up old contexts
    cleanupOldContexts(): void {
        const now = Date.now();
        const maxAge = 3600000; // 1 hour

        for (const [callSid, context] of this.contextStore.entries()) {
            if (now - context.timestamp > maxAge) {
                this.contextStore.delete(callSid);
                this.recoveryAttempts.delete(callSid);
                console.log(`🧹 [${callSid}] Cleaned up old session context`);
            }
        }
    }

    // Get comprehensive recovery message with full conversation history
    getRecoveryMessage(callSid: string): string | null {
        const context = this.contextStore.get(callSid);
        if (!context || !context.recoveryInfo.wasInterrupted) {
            return null;
        }

        const recoveryCount = context.recoveryInfo.recoveryCount;
        const interruptionReason = context.recoveryInfo.interruptionReason || 'connection issue';

        // Build complete conversation history for full context restoration
        let fullConversationHistory = '';
        let speechTranscriptHistory = '';

        // Include conversation log
        if (context.conversationState?.conversationLog && context.conversationState.conversationLog.length > 0) {
            const conversationLog = context.conversationState.conversationLog.map(msg => {
                const speaker = msg.role === 'assistant' ? 'AI Assistant' : 'Customer';
                const timestamp = new Date(msg.timestamp).toLocaleTimeString();
                return `[${timestamp}] ${speaker}: ${msg.content}`;
            }).join('\n');
            fullConversationHistory += `\n\nCONVERSATION LOG:\n${conversationLog}\n`;
        }

        // Include full transcript if available
        if (context.conversationState?.fullTranscript && context.conversationState.fullTranscript.length > 0) {
            const transcriptLog = context.conversationState.fullTranscript.map(msg => {
                const speaker = msg.role === 'assistant' ? 'AI Assistant' : 'Customer';
                const timestamp = new Date(msg.timestamp).toLocaleTimeString();
                const confidence = msg.confidence ? ` (${Math.round(msg.confidence * 100)}% confidence)` : '';
                return `[${timestamp}] ${speaker}${confidence}: ${msg.content}`;
            }).join('\n');
            fullConversationHistory += `\n\nFULL TRANSCRIPT:\n${transcriptLog}\n`;
        }

        // Include speech transcriptions for enhanced context
        if (context.conversationState?.speechTranscript && context.conversationState.speechTranscript.length > 0) {
            const speechLog = context.conversationState.speechTranscript.map(speech => {
                const timestamp = new Date(speech.timestamp).toLocaleTimeString();
                const confidence = Math.round(speech.confidence * 100);
                return `[${timestamp}] USER SPEECH (${confidence}% confidence): ${speech.text}`;
            }).join('\n');
            speechTranscriptHistory = `\n\nSPEECH TRANSCRIPTIONS:\n${speechLog}\n`;
        }

        // Include original AI instructions from campaign script
        const originalInstructions = context.sessionConfig?.originalAIInstructions || context.sessionConfig?.aiInstructions || '';
        const sessionDuration = Math.round((Date.now() - context.connectionState.sessionStartTime) / 1000);

        // Recovery message should come from campaign script or environment variable
        return process.env.RECOVERY_MESSAGE || originalInstructions || '';
    }

    // Clear session context
    clearSessionContext(callSid: string): void {
        this.contextStore.delete(callSid);
        this.recoveryAttempts.delete(callSid);
        console.log(`🗑️ [${callSid}] Session context cleared`);
    }

    // Get recovery status for monitoring
    getRecoveryStatus(callSid: string): {
        hasContext: boolean;
        canRecover: boolean;
        recoveryCount: number;
        maxRecoveryAttempts: number;
        wasInterrupted: boolean;
        lastActivity: number | undefined;
        interruptionReason: string | null | undefined;
    } {
        const context = this.contextStore.get(callSid);
        const recoveryCount = this.recoveryAttempts.get(callSid) || 0;
        
        return {
            hasContext: !!context,
            canRecover: this.canRecover(callSid),
            recoveryCount,
            maxRecoveryAttempts: this.maxRecoveryAttempts,
            wasInterrupted: context?.recoveryInfo?.wasInterrupted || false,
            lastActivity: context?.connectionState?.lastActivity,
            interruptionReason: context?.recoveryInfo?.interruptionReason
        };
    }

    // Get context statistics
    getContextStats(): {
        totalContexts: number;
        activeRecoveries: number;
        oldestContext: number;
        newestContext: number;
    } {
        const contexts = Array.from(this.contextStore.values());
        return {
            totalContexts: this.contextStore.size,
            activeRecoveries: contexts.filter(ctx => ctx.recoveryInfo.wasInterrupted).length,
            oldestContext: Math.min(...contexts.map(ctx => ctx.timestamp)),
            newestContext: Math.max(...contexts.map(ctx => ctx.timestamp))
        };
    }

    /**
     * Clear all contexts (for shutdown)
     */
    clearAllContexts(): void {
        const contextCount = this.contextStore.size;
        const recoveryCount = this.recoveryAttempts.size;
        
        this.contextStore.clear();
        this.recoveryAttempts.clear();
        
        console.log(`🧹 ContextManager: Cleared ${contextCount} contexts and ${recoveryCount} recovery attempts`);
    }
}