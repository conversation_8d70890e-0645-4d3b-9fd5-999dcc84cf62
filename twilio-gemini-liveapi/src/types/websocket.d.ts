// WebSocket Types

import { WebSocket } from 'ws';
import { ConnectionData } from './global';
import { SessionManager } from '../session/session-manager';
import { ContextManager } from '../session/context-manager';
import { SummaryManager } from '../session/summary-manager';
import { HealthMonitor } from '../session/health-monitor';
import { LifecycleManager } from '../session/lifecycle-manager';
import { RecoveryManager } from '../session/recovery-manager';
import { TranscriptionManager } from '../audio/transcription-manager';
import { ScriptManager } from '../scripts/script-manager';
import { VoiceManager } from '../gemini/voice-manager';
import { ModelManager } from '../gemini/model-manager';
import { GeminiClient } from '../gemini/client';

export interface WebSocketConnection {
  socket?: WebSocket;
  query?: Record<string, any>;
}

// ConnectionData is now imported from global.d.ts
export { ConnectionData };

export interface SessionConfig {
  aiInstructions: string;
  voice: string;
  model: string;
  targetName?: string | null;
  targetPhoneNumber?: string | null;
  scriptType: string;
  scriptId: string;
  isIncomingCall?: boolean;
  isTestMode?: boolean;
}

export interface WebSocketDependencies {
  sessionManager: SessionManager;
  contextManager: ContextManager;
  activeConnections: Map<string, ConnectionData>;
  healthMonitor: HealthMonitor;
  summaryManager: SummaryManager;
  lifecycleManager: LifecycleManager;
  recoveryManager: RecoveryManager;
  transcriptionManager: TranscriptionManager;
  scriptManager: ScriptManager;
  voiceManager: VoiceManager;
  modelManager: ModelManager;
  getNextCallConfig?: () => SessionConfig | null;
  twilioHelper?: any;
  GEMINI_DEFAULT_VOICE: string;
  GEMINI_DEFAULT_MODEL: string;
  SUMMARY_GENERATION_PROMPT?: string;
  config?: any;
  geminiClient?: GeminiClient;
}

export interface FlowDependencies extends WebSocketDependencies {
  flowType: string;
  getSessionConfig: () => SessionConfig;
  isIncomingCall: boolean;
  callType?: string;
  isTestMode?: boolean;
  getOutboundTestConfig?: (deps: WebSocketDependencies) => SessionConfig;
  getInboundTestConfig?: (deps: WebSocketDependencies) => SessionConfig;
}

export interface TwilioMediaMessage {
  event: 'media';
  media: {
    payload: string;
    chunk?: string;
    timestamp?: string;
    track?: string;
  };
  streamSid?: string;
  sequenceNumber?: string;
}

export interface TwilioStartMessage {
  event: 'start';
  start: {
    streamSid: string;
    accountSid: string;
    callSid: string;
    tracks?: string[];
    customParameters?: Record<string, any>;
  };
  streamSid?: string;
}

export interface LocalAudioMessage {
  type: 'audio-data' | 'audio';
  audio?: string;
  audioData?: string;
}

export interface LocalStartMessage {
  type: 'start-session';
  aiInstructions?: string;
  voice?: string;
  model?: string;
  scriptId?: string;
}

export interface LocalTextMessage {
  type: 'text-message';
  text: string;
}

export interface HeartbeatData {
  ws: WebSocket;
  interval: number;
  timeout: number;
  onTimeout: ((sessionId: string, ws: WebSocket) => void) | null;
  intervalId: NodeJS.Timeout | null;
  timeoutId: NodeJS.Timeout | null;
  lastPong: number;
  missedPings: number;
  pongHandler?: () => void;
}

export interface HeartbeatStatus {
  sessionId: string;
  lastPong: number;
  missedPings: number;
  interval: number;
  timeout: number;
  isActive: boolean;
}

export interface HeartbeatStatistics {
  totalSessions: number;
  healthySessions: number;
  unhealthySessions: number;
  averageLastPong: number;
}

export interface SessionData {
  geminiSession: any;
  isSessionActive: boolean;
}