import { websocketLogger as logger } from '../utils/logger';
import type { WebSocket } from 'ws';
import type { HeartbeatData, HeartbeatStatus, HeartbeatStatistics } from '../types/websocket';

/**
 * WebSocket Heartbeat Manager
 * Provides ping-pong functionality to detect stale connections
 */
export class HeartbeatManager {
    private connections: Map<string, HeartbeatData>;
    private defaultInterval: number;
    private defaultTimeout: number;

    constructor() {
        this.connections = new Map(); // sessionId -> heartbeat data
        this.defaultInterval = 30000; // 30 seconds
        this.defaultTimeout = 10000;  // 10 seconds for pong response
    }

    /**
     * Start heartbeat monitoring for a WebSocket connection
     */
    startHeartbeat(
        sessionId: string, 
        ws: WebSocket, 
        interval: number | null = null, 
        timeout: number | null = null, 
        onTimeout: ((sessionId: string, ws: WebSocket) => void) | null = null
    ): void {
        if (!sessionId || !ws) {
            logger.error('❌ Cannot start heartbeat: missing sessionId or WebSocket');
            return;
        }

        const heartbeatInterval = interval || this.defaultInterval;
        const heartbeatTimeout = timeout || this.defaultTimeout;

        // Clean up existing heartbeat if any
        this.stopHeartbeat(sessionId);

        const heartbeatData: HeartbeatData = {
            ws,
            interval: heartbeatInterval,
            timeout: heartbeatTimeout,
            onTimeout,
            intervalId: null,
            timeoutId: null,
            lastPong: Date.now(),
            missedPings: 0
        };

        // Set up pong handler with proper cleanup
        const pongHandler = () => {
            logger.debug(`💓 [${sessionId}] Received pong`);
            heartbeatData.lastPong = Date.now();
            heartbeatData.missedPings = 0;
            
            // Clear timeout since we got a response
            if (heartbeatData.timeoutId) {
                clearTimeout(heartbeatData.timeoutId);
                heartbeatData.timeoutId = null;
            }
        };
        
        ws.on('pong', pongHandler);
        heartbeatData.pongHandler = pongHandler; // Store for cleanup

        // Start ping interval
        heartbeatData.intervalId = setInterval(() => {
            this.sendPing(sessionId);
        }, heartbeatInterval);

        this.connections.set(sessionId, heartbeatData);
        logger.info(`💓 [${sessionId}] Heartbeat started (interval: ${heartbeatInterval}ms, timeout: ${heartbeatTimeout}ms)`);
    }

    /**
     * Send a ping to the WebSocket connection
     */
    private sendPing(sessionId: string): void {
        const heartbeatData = this.connections.get(sessionId);
        if (!heartbeatData) {
            logger.warn(`⚠️ [${sessionId}] Cannot send ping: no heartbeat data found`);
            return;
        }

        const { ws, timeout, onTimeout } = heartbeatData;

        if (ws.readyState !== 1) { // WebSocket.OPEN
            logger.warn(`⚠️ [${sessionId}] WebSocket not open, stopping heartbeat`);
            this.stopHeartbeat(sessionId);
            return;
        }

        try {
            logger.debug(`💓 [${sessionId}] Sending ping`);
            ws.ping();
            heartbeatData.missedPings++;

            // Set timeout for pong response
            heartbeatData.timeoutId = setTimeout(() => {
                logger.warn(`⚠️ [${sessionId}] Heartbeat timeout - no pong received within ${timeout}ms`);
                
                if (heartbeatData.missedPings >= 3) {
                    logger.error(`❌ [${sessionId}] Connection appears dead (${heartbeatData.missedPings} missed pings)`);
                    this.stopHeartbeat(sessionId);
                    
                    if (onTimeout && typeof onTimeout === 'function') {
                        try {
                            onTimeout(sessionId, ws);
                        } catch (error) {
                            logger.error(`❌ [${sessionId}] Error in heartbeat timeout callback:`, error);
                        }
                    }
                }
            }, timeout);

        } catch (error) {
            logger.error(`❌ [${sessionId}] Error sending ping:`, error);
            this.stopHeartbeat(sessionId);
        }
    }

    /**
     * Stop heartbeat monitoring for a session
     */
    stopHeartbeat(sessionId: string): void {
        const heartbeatData = this.connections.get(sessionId);
        if (!heartbeatData) {
            return; // Already stopped or never started
        }

        // Clear interval
        if (heartbeatData.intervalId) {
            clearInterval(heartbeatData.intervalId);
        }

        // Clear timeout
        if (heartbeatData.timeoutId) {
            clearTimeout(heartbeatData.timeoutId);
        }
        
        // Remove event listeners to prevent memory leak
        if (heartbeatData.pongHandler && heartbeatData.ws) {
            heartbeatData.ws.removeListener('pong', heartbeatData.pongHandler);
        }

        this.connections.delete(sessionId);
        logger.info(`💓 [${sessionId}] Heartbeat stopped and cleaned up`);
    }

    /**
     * Get heartbeat status for a session
     */
    getHeartbeatStatus(sessionId: string): HeartbeatStatus | null {
        const heartbeatData = this.connections.get(sessionId);
        if (!heartbeatData) {
            return null;
        }

        return {
            sessionId,
            lastPong: heartbeatData.lastPong,
            missedPings: heartbeatData.missedPings,
            interval: heartbeatData.interval,
            timeout: heartbeatData.timeout,
            isActive: heartbeatData.intervalId !== null
        };
    }

    /**
     * Get all active heartbeat sessions
     */
    getActiveSessions(): string[] {
        return Array.from(this.connections.keys());
    }

    /**
     * Clean up all heartbeats (useful for shutdown)
     */
    stopAllHeartbeats(): void {
        const sessions = this.getActiveSessions();
        logger.info(`💓 Stopping ${sessions.length} active heartbeats`);
        
        sessions.forEach(sessionId => {
            this.stopHeartbeat(sessionId);
        });
    }

    /**
     * Get heartbeat statistics
     */
    getStatistics(): HeartbeatStatistics {
        const sessions = Array.from(this.connections.values());
        const now = Date.now();
        
        return {
            totalSessions: sessions.length,
            healthySessions: sessions.filter(s => s.missedPings === 0).length,
            unhealthySessions: sessions.filter(s => s.missedPings > 0).length,
            averageLastPong: sessions.length > 0 ? 
                sessions.reduce((sum, s) => sum + (now - s.lastPong), 0) / sessions.length : 0
        };
    }
}

// Global heartbeat manager instance
export const globalHeartbeatManager = new HeartbeatManager();