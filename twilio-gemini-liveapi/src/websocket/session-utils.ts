import { websocketLogger } from '../utils/logger';
import { globalHeartbeatManager } from './heartbeat-manager';
import type { WebSocketDependencies, ConnectionData } from '../types/websocket';

export async function endSession(
    sessionId: string, 
    deps: WebSocketDependencies, 
    reason: string = 'user_requested'
): Promise<void> {
    const {
        activeConnections,
        sessionManager,
        contextManager,
        summaryManager,
        transcriptionManager,
        lifecycleManager
    } = deps;

    websocketLogger.info(`Ending session`, { sessionId, reason });

    try {
        const connectionData = activeConnections.get(sessionId);
        if (!connectionData) {
            websocketLogger.warn(`Session not found for cleanup`, { sessionId });
            return;
        }

        // Stop heartbeat monitoring
        globalHeartbeatManager.stopHeartbeat(sessionId);

        // Clean up transcription
        if (transcriptionManager && connectionData.deepgramConnection) {
            transcriptionManager.closeTranscription(sessionId);
        }

        // Generate summary if requested
        if (summaryManager && connectionData.summaryRequested && !connectionData.summaryReceived) {
            try {
                await summaryManager.generateSummary(sessionId, connectionData, contextManager);
            } catch (error) {
                websocketLogger.error(`Error generating summary`, error, { sessionId });
            }
        }

        // Clean up Gemini session
        if (sessionManager && connectionData.geminiSession) {
            try {
                await sessionManager.cleanupSession(sessionId);
            } catch (error) {
                websocketLogger.error(`Error cleaning up Gemini session`, error, { sessionId });
            }
        }

        // Clean up context
        if (contextManager) {
            (contextManager as any).clearContext(sessionId);
        }

        // Remove from active connections
        activeConnections.delete(sessionId);

        websocketLogger.info(`Session ended successfully`, { sessionId, reason });

    } catch (error) {
        websocketLogger.error(`Error during session cleanup`, error, { sessionId });
    }
}

export function getConnectionData(
    sessionId: string, 
    activeConnections: Map<string, ConnectionData>
): ConnectionData | undefined {
    return activeConnections.get(sessionId);
}

export function updateConnectionActivity(
    sessionId: string, 
    activeConnections: Map<string, ConnectionData>
): void {
    const connectionData = activeConnections.get(sessionId);
    if (connectionData) {
        connectionData.lastActivity = Date.now();
    }
}

export function isSessionActive(
    sessionId: string, 
    activeConnections: Map<string, ConnectionData>
): boolean {
    const connectionData = activeConnections.get(sessionId);
    return !!(connectionData && connectionData.isSessionActive);
}